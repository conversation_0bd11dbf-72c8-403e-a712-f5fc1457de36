// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://dwgolyqevdaqosteonfl.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImR3Z29seXFldmRhcW9zdGVvbmZsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgzNzAyNTUsImV4cCI6MjA2Mzk0NjI1NX0.F2s4SgLcrc-iFsrzMstYTvDdG_Iq52Yt1RdFM-wf6iE";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);