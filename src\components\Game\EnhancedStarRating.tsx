import React, { useState } from 'react';
import { Star } from 'lucide-react';
import { Language } from '@/types/common';

export type RatingType = 'clarity' | 'helpfulness';

interface EnhancedStarRatingProps {
  onRating: (rating: number) => void;
  ratingType: RatingType;
  language: Language;
  initialRating?: number;
  size?: number;
}

const ratingLabels = {
  clarity: {
    EN: {
      title: 'Rate the Clarity of Feedback',
      labels: ['Very confusing', 'Confusing', 'Moderate', 'Clear', 'Very clear']
    },
    DE: {
      title: 'Bewerten Sie die Klarheit des Feedbacks',
      labels: ['Sehr verwirrend', 'Verwirrend', 'M<PERSON>ßig', 'Klar', 'Sehr klar']
    }
  },
  helpfulness: {
    EN: {
      title: 'Rate How Helpful You Found the Feedback',
      labels: ['Bad', 'Not helpful', 'Helpful', 'Very helpful', 'Excellent']
    },
    DE: {
      title: 'Bewerten Sie, wie hilfreich Sie das Feedback fanden',
      labels: ['<PERSON><PERSON><PERSON><PERSON>', '<PERSON>cht hilfreich', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>hr hilfreich', 'Ausgezeichnet']
    }
  }
};

const EnhancedStarRating: React.FC<EnhancedStarRatingProps> = ({ 
  onRating, 
  ratingType,
  language,
  initialRating = 0,
  size = 20 
}) => {
  const [rating, setRating] = useState(initialRating);
  const [hoverRating, setHoverRating] = useState(0);

  const labels = ratingLabels[ratingType][language];

  const handleStarClick = (starValue: number) => {
    setRating(starValue);
    onRating(starValue);
  };

  const handleStarHover = (starValue: number) => {
    setHoverRating(starValue);
  };

  const handleMouseLeave = () => {
    setHoverRating(0);
  };

  return (
    <div className="space-y-3">
      {/* Title */}
      <p className="text-xs font-medium text-gray-700 dark:text-gray-300">
        {labels.title}:
      </p>
      
      {/* Stars */}
      <div className="flex items-center justify-center space-x-2" onMouseLeave={handleMouseLeave}>
        {[1, 2, 3, 4, 5].map((starValue) => (
          <div key={starValue} className="flex flex-col items-center space-y-1">
            <button
              onClick={() => handleStarClick(starValue)}
              onMouseEnter={() => handleStarHover(starValue)}
              className="transition-all duration-150 hover:scale-110 transform focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 rounded"
            >
              <Star
                size={size}
                className={`${
                  starValue <= (hoverRating || rating)
                    ? 'fill-yellow-400 text-yellow-400'
                    : 'text-gray-300 dark:text-gray-600'
                } transition-colors duration-150`}
              />
            </button>
            
            {/* Label under each star */}
            <span className={`text-xs text-center leading-tight max-w-[60px] ${
              starValue <= (hoverRating || rating)
                ? 'text-gray-800 dark:text-gray-200 font-medium'
                : 'text-gray-500 dark:text-gray-500'
            } transition-colors duration-150`}>
              {labels.labels[starValue - 1]}
            </span>
          </div>
        ))}
      </div>
      
      {/* Current rating display */}
      {(rating > 0 || hoverRating > 0) && (
        <div className="text-center">
          <span className="text-xs text-gray-600 dark:text-gray-400">
            {hoverRating > 0 ? labels.labels[hoverRating - 1] : labels.labels[rating - 1]}
          </span>
        </div>
      )}
    </div>
  );
};

export default EnhancedStarRating;
