
import { supabase } from '@/integrations/supabase/client';

export interface FeedbackRating {
  user_id: string;
  feedback_type: 'graph' | 'graphrag';
  rating_type: 'clarity' | 'helpfulness';
  rating: number;
  clarity_rating?: number;
  helpfulness_rating?: number;
  model_used?: string;
  generated_text?: string;
  item_id?: string;
  session_id?: string;
}

export const feedbackService = {
  async saveFeedbackRating(rating: FeedbackRating): Promise<boolean> {
    try {
      // Prepare the data for insertion
      const insertData: any = {
        user_id: rating.user_id,
        feedback_type: rating.feedback_type,
        rating_type: rating.rating_type,
        rating: rating.rating,
        item_id: rating.item_id,
        session_id: rating.session_id,
        model_used: rating.model_used,
        generated_text: rating.generated_text
      };

      // Set the appropriate rating column based on rating_type
      if (rating.rating_type === 'clarity') {
        insertData.clarity_rating = rating.rating;
      } else if (rating.rating_type === 'helpfulness') {
        insertData.helpfulness_rating = rating.rating;
      }

      const { error } = await supabase
        .from('feedback_ratings')
        .insert([insertData]);

      if (error) {
        console.error('Error saving feedback rating:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error in saveFeedbackRating:', error);
      return false;
    }
  },

  async saveLLMResponse(data: {
    user_id: string;
    feedback_type: 'graph' | 'graphrag';
    model_used: string;
    generated_text: string;
    item_id?: string;
    session_id?: string;
  }): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('feedback_ratings')
        .insert([{
          user_id: data.user_id,
          feedback_type: data.feedback_type,
          model_used: data.model_used,
          generated_text: data.generated_text,
          item_id: data.item_id,
          session_id: data.session_id,
          rating: 0, // Placeholder rating for LLM response logging
          rating_type: 'clarity' // Default type for logging
        }]);

      if (error) {
        console.error('Error saving LLM response:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error in saveLLMResponse:', error);
      return false;
    }
  },

  async getUserFeedbackRatings(userId: string): Promise<FeedbackRating[]> {
    try {
      const { data, error } = await supabase
        .from('feedback_ratings')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching feedback ratings:', error);
        return [];
      }

      // Type cast the feedback_type and rating_type to ensure they match our interface
      return (data || []).map(item => ({
        ...item,
        feedback_type: item.feedback_type as 'graph' | 'graphrag',
        rating_type: item.rating_type as 'clarity' | 'helpfulness'
      }));
    } catch (error) {
      console.error('Error in getUserFeedbackRatings:', error);
      return [];
    }
  }
};
