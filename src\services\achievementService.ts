import { supabase } from '@/integrations/supabase/client';
import { gameService } from './gameService';

export interface Achievement {
  id: string;
  name_en: string;
  name_de: string;
  description_en: string;
  description_de: string;
  emoji: string;
  condition_type: string;
  condition_value: number;
}

export interface UserAchievement {
  id: string;
  user_id: string;
  achievement_id: string;
  unlocked_at: string;
  achievements: Achievement;
}

export const achievementService = {
  async checkAndUnlockAchievements(userId: string, gameData: {
    totalCorrect: number;
    currentStreak: number;
    bestStreak: number;
    bestScore: number;
    lastCorrectBinType?: string;
  }): Promise<Achievement[]> {
    console.log('Checking achievements for user:', userId, gameData);
    
    try {
      // Get all achievements
      const achievements = await gameService.getAchievements();
      const userAchievements = await gameService.getUserAchievements(userId);
      
      // Get IDs of already unlocked achievements
      const unlockedIds = userAchievements.map(ua => ua.achievement_id);
      
      const newlyUnlocked: Achievement[] = [];
      
      for (const achievement of achievements) {
        // Skip if already unlocked
        if (unlockedIds.includes(achievement.id)) {
          continue;
        }
        
        let shouldUnlock = false;
        
        switch (achievement.condition_type) {
          case 'total_correct':
            shouldUnlock = gameData.totalCorrect >= achievement.condition_value;
            break;
            
          case 'best_streak':
            shouldUnlock = gameData.bestStreak >= achievement.condition_value;
            break;
            
          case 'current_streak':
            shouldUnlock = gameData.currentStreak >= achievement.condition_value;
            break;
            
          case 'best_score':
            shouldUnlock = gameData.bestScore >= achievement.condition_value;
            break;
            
          case 'bio_streak':
          case 'paper_streak':
          case 'plastic_streak':
            // For specific bin type streaks, we'd need to track these separately
            // For now, we'll use the general current_streak as a placeholder
            shouldUnlock = gameData.currentStreak >= achievement.condition_value;
            break;
            
          case 'daily_streak':
            // This would require tracking daily login/play streaks
            // For now, we'll skip this type
            break;
            
          default:
            console.warn('Unknown achievement condition type:', achievement.condition_type);
        }
        
        if (shouldUnlock) {
          console.log('Unlocking achievement:', achievement.id);
          await gameService.unlockAchievement(userId, achievement.id);
          newlyUnlocked.push(achievement);
        }
      }
      
      return newlyUnlocked;
    } catch (error) {
      console.error('Error checking achievements:', error);
      return [];
    }
  },

  async getUnlockedAchievements(userId: string): Promise<UserAchievement[]> {
    try {
      return await gameService.getUserAchievements(userId);
    } catch (error) {
      console.error('Error getting unlocked achievements:', error);
      return [];
    }
  },

  async getAllAchievements(): Promise<Achievement[]> {
    try {
      return await gameService.getAchievements();
    } catch (error) {
      console.error('Error getting all achievements:', error);
      return [];
    }
  }
};
